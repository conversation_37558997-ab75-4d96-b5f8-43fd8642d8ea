import React from 'react';
import { Button, Dropdown, MenuProps } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { llmItems } from '../constants';

interface LLMSelectorProps {
  llmDefaultSelect: string;
  onLlmChange: (value: string) => void;
  disabled?: boolean;
}

const LLMSelector: React.FC<LLMSelectorProps> = ({
  llmDefaultSelect,
  onLlmChange,
  disabled = false
}) => {
  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    onLlmChange(key as string);
  };

  const getCurrentLLMLabel = () => {
    const currentItem = llmItems.find(item => item?.key === llmDefaultSelect);
    if (currentItem && React.isValidElement(currentItem.label)) {
      const labelElement = currentItem.label as React.ReactElement;
      const titleElement = labelElement.props.children.find((child: any) => 
        React.isValidElement(child) && child.props.className === 'title'
      );
      return titleElement ? titleElement.props.children : llmDefaultSelect;
    }
    return llmDefaultSelect;
  };

  return (
    <Dropdown
      menu={{
        items: llmItems,
        onClick: handleMenuClick,
      }}
      disabled={disabled}
      trigger={['click']}
    >
      <Button>
        {getCurrentLLMLabel()}
        <DownOutlined />
      </Button>
    </Dropdown>
  );
};

export default LLMSelector;
